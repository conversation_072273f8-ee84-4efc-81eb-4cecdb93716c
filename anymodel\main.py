import json
import os
import time
import uuid
import threading
from typing import Any, <PERSON>ync<PERSON><PERSON><PERSON>, Dict, List, Optional

import httpx
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.responses import StreamingResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

# Configuration
ANYMODEL_API_URL = "https://app.anymodel.xyz/api/text"

# Global variables for client API keys and Anymodel tokens
VALID_CLIENT_KEYS: set = set()
ANYMODEL_TOKENS: list = []
current_anymodel_token_index: int = 0
token_rotation_lock = threading.Lock()
models_data: Dict[str, List[Any]] = {"data": []}

# Pydantic Models
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    stream: bool = False
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None

class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str

class ModelList(BaseModel):
    object: str = "list"
    data: List[ModelInfo]

class ChatCompletionChoice(BaseModel):
    message: ChatMessage
    index: int = 0
    finish_reason: str = "stop"

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: Dict[str, int] = Field(default_factory=lambda: {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0})

class StreamChoice(BaseModel):
    delta: Dict[str, Any] = Field(default_factory=dict)
    index: int = 0
    finish_reason: Optional[str] = None

class StreamResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[StreamChoice]

# FastAPI App
app = FastAPI(title="Anymodel OpenAI API Adapter")
security = HTTPBearer(auto_error=False)

def load_models() -> Dict[str, List[ModelInfo]]:
    """Load models from models.json, filtering for 'base' tier text models"""
    global models_data
    filtered_model_list: List[ModelInfo] = []
    try:
        with open("models.json", "r", encoding="utf-8") as f:
            raw_models_data = json.load(f)
        
        text_model_list = raw_models_data.get("textModelList", [])
        
        for provider_group in text_model_list:
            provider_name = provider_group.get("name", "unknown_provider")
            for model_entry in provider_group.get("models", []):
                # Check tier, availability, and ensure it's not marked as unavailable explicitly
                is_unavailable_flag = model_entry.get("unavailable")
                is_hidden = isinstance(is_unavailable_flag, str) and is_unavailable_flag.upper() == "HIDDEN"
                
                if model_entry.get("tier") == "base" and not is_unavailable_flag and not is_hidden:
                    model_id = model_entry.get("name")
                    if model_id:
                        # 'created' field is not reliably available or in correct format in source models.json
                        # Using current time as a fallback.
                        created_timestamp = int(time.time())
                        # owned_by is the provider group name
                        filtered_model_list.append(
                            ModelInfo(
                                id=model_id,
                                created=created_timestamp,
                                owned_by=provider_name
                            )
                        )
        
        models_data = {"data": filtered_model_list}
        print(f"Successfully loaded {len(filtered_model_list)} base-tier, available text models from models.json")
            
    except FileNotFoundError:
        print("Error: models.json not found. No models will be available.")
        models_data = {"data": []}
    except json.JSONDecodeError as e:
        print(f"Error decoding models.json: {e}. No models will be available.")
        models_data = {"data": []}
    except Exception as e:
        print(f"An unexpected error occurred loading models.json: {e}")
        models_data = {"data": []}
    return models_data

def load_client_api_keys():
    """Load client API keys from client_api_keys.json"""
    global VALID_CLIENT_KEYS
    try:
        with open("client_api_keys.json", "r", encoding="utf-8") as f:
            keys = json.load(f)
            if not isinstance(keys, list):
                print("Warning: client_api_keys.json should contain a list of keys.")
                VALID_CLIENT_KEYS = set()
                return
            VALID_CLIENT_KEYS = set(keys)
            print(f"Successfully loaded {len(VALID_CLIENT_KEYS)} client API keys.")
    except FileNotFoundError:
        print("Error: client_api_keys.json not found. Client authentication will fail.")
        VALID_CLIENT_KEYS = set()
    except Exception as e:
        print(f"Error loading client_api_keys.json: {e}")
        VALID_CLIENT_KEYS = set()

def load_anymodel_tokens():
    """Load Anymodel tokens from anymodel.txt"""
    global ANYMODEL_TOKENS
    try:
        with open("anymodel.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()
            loaded_tokens = []
            for line in lines:
                stripped_line = line.strip()
                if stripped_line and not stripped_line.startswith("#"): # Ignore empty lines and comments
                    token_part = stripped_line.split("----")[0]
                    if token_part:
                        loaded_tokens.append(token_part)
            
            ANYMODEL_TOKENS = loaded_tokens
            if ANYMODEL_TOKENS:
                print(f"Successfully loaded {len(ANYMODEL_TOKENS)} Anymodel tokens from anymodel.txt.")
            else:
                print("Warning: anymodel.txt was found, but no valid tokens were loaded. Anymodel API calls may fail.")

    except FileNotFoundError:
        print("Error: anymodel.txt not found. Anymodel API calls will fail.")
        ANYMODEL_TOKENS = []
    except Exception as e:
        print(f"Error loading anymodel.txt: {e}")
        ANYMODEL_TOKENS = []

def get_model_item(model_id: str) -> Optional[ModelInfo]:
    """Get model item by ID from loaded models data"""
    for model_info_obj in models_data.get("data", []):
        if model_info_obj.id == model_id:
            return model_info_obj
    return None

async def authenticate_client(auth: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """Authenticate client based on API key in Authorization header"""
    if not VALID_CLIENT_KEYS:
        print("Critical: No client API keys configured. Denying all requests.")
        raise HTTPException(status_code=503, detail="Service unavailable: Client API keys not configured on server.")
    
    if not auth or not auth.credentials:
        raise HTTPException(
            status_code=401,
            detail="API key required in Authorization header.",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if auth.credentials not in VALID_CLIENT_KEYS:
        raise HTTPException(status_code=403, detail="Invalid client API key.")

def get_next_anymodel_token() -> str:
    """Get the next Anymodel token using round-robin"""
    global current_anymodel_token_index
    
    if not ANYMODEL_TOKENS:
        raise HTTPException(status_code=503, detail="Service unavailable: No Anymodel tokens configured on server.")
    
    with token_rotation_lock:
        token_to_use = ANYMODEL_TOKENS[current_anymodel_token_index]
        current_anymodel_token_index = (current_anymodel_token_index + 1) % len(ANYMODEL_TOKENS)
    return token_to_use

@app.on_event("startup")
async def startup():
    """应用启动时初始化配置"""
    print("Starting Anymodel OpenAI API Adapter server...")
    load_models()
    load_client_api_keys()
    load_anymodel_tokens()
    print("Server initialization completed.")

@app.get("/v1/models", response_model=ModelList)
async def list_v1_models(_: None = Depends(authenticate_client)):
    """List available models - authenticated"""
    return await get_models_list_response()

@app.get("/models", response_model=ModelList)
async def list_models_no_auth():
    """List available models without authentication - for client compatibility"""
    return await get_models_list_response()

async def get_models_list_response() -> ModelList:
    """Helper to construct ModelList response"""
    loaded_model_info_objects = models_data.get("data", [])
    
    if not loaded_model_info_objects:
        print("Warning: No base-tier models loaded or available from models.json, using fallback default models for /models endpoint.")
        # Fallback default models are more for ensuring the endpoint doesn't crash than for actual use.
        fallback_data = [
            ModelInfo(id="openai/gpt-4o-mini", created=int(time.time()), owned_by="openai"),
            ModelInfo(id="openai/gpt-3.5-turbo", created=int(time.time()), owned_by="openai"),
            ModelInfo(id="anthropic/claude-3-5-sonnet-20240620", created=int(time.time()), owned_by="anthropic"),
            ModelInfo(id="anthropic/claude-3-5-haiku-20241022", created=int(time.time()), owned_by="anthropic"),
            ModelInfo(id="cohere/command-r", created=int(time.time()), owned_by="cohere"),
            ModelInfo(id="xai/grok-2-1212", created=int(time.time()), owned_by="xai")
        ]
        return ModelList(data=fallback_data)
    
    return ModelList(data=loaded_model_info_objects)

@app.post("/v1/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest,
    _: None = Depends(authenticate_client)
):
    """Create chat completion using Anymodel backend"""
    if not get_model_item(request.model):
        raise HTTPException(status_code=404, detail=f"Model '{request.model}' not found or not available. Ensure it is a configured base-tier model.")
    
    anymodel_token = get_next_anymodel_token()
    
    if not request.messages:
        raise HTTPException(status_code=400, detail="No messages provided in the request.")

    # Extract current prompt from last message
    current_prompt_content = request.messages[-1].content
    
    # Build history from previous messages (user/assistant pairs)
    history_list_for_anymodel = []
    processed_messages_for_history = request.messages[:-1]
    i = 0
    while i < len(processed_messages_for_history) - 1:
        msg1 = processed_messages_for_history[i]
        msg2 = processed_messages_for_history[i+1]
        if msg1.role == "user" and msg2.role == "assistant":
            history_list_for_anymodel.append({
                "prompt": msg1.content,
                "response": msg2.content,
                "functionCalls": [],
                "functionResponses": []
            })
            i += 2
        else:
            i += 1

    # Construct Anymodel payload
    anymodel_payload = {
        "prompt": current_prompt_content,
        "image": None, 
        "pdf": None,   
        "options": {
            "models": [request.model], 
            "generatePromptSummary": False,
        },
        "history": {request.model: history_list_for_anymodel} if history_list_for_anymodel else {},
    }
    
    # Set headers for Anymodel API
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "application/json",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "pragma": "no-cache",
        "cache-control": "no-cache",
        "sec-ch-ua-platform": '"Windows"',
        "authorization": anymodel_token,
        "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "origin": "https://app.anymodel.xyz",
        "sec-fetch-site": "same-origin",
        "sec-fetch-mode": "cors",
        "sec-fetch-dest": "empty",
        "referer": "https://app.anymodel.xyz/",
        "accept-language": "zh-CN,zh;q=0.9",
        "priority": "u=1, i",
    }
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(ANYMODEL_API_URL, json=anymodel_payload, headers=headers)
            response.raise_for_status()
            response_json = response.json()
            
            # Extract content from Anymodel response
            full_content = ""
            if response_json.get("responses") and len(response_json["responses"]) > 0:
                first_response_item = response_json["responses"][0]
                if isinstance(first_response_item, dict):
                     full_content = first_response_item.get("content", "")
                else:
                    print(f"Warning: Unexpected item type in Anymodel 'responses' list: {type(first_response_item)}. Full response: {response_json}")
                    full_content = str(first_response_item) if first_response_item else ""
            else:
                print(f"Warning: Anymodel response does not contain 'responses' list or it's empty. Full response: {response_json}")

            # Handle streaming vs non-streaming response
            if request.stream:
                # Fake streaming response
                async def stream_response_generator():
                    stream_id = f"chatcmpl-{uuid.uuid4().hex}"
                    created_time = int(time.time())
                    
                    # Send role delta
                    yield f"data: {StreamResponse(id=stream_id, object='chat.completion.chunk', created=created_time, model=request.model, choices=[StreamChoice(delta={'role': 'assistant'})]).json()}\n\n"
                    
                    # Send content delta
                    if full_content:
                         yield f"data: {StreamResponse(id=stream_id, object='chat.completion.chunk', created=created_time, model=request.model, choices=[StreamChoice(delta={'content': full_content})]).json()}\n\n"
                    
                    # Send finish reason
                    yield f"data: {StreamResponse(id=stream_id, object='chat.completion.chunk', created=created_time, model=request.model, choices=[StreamChoice(delta={}, finish_reason='stop')]).json()}\n\n"
                    yield "data: [DONE]\n\n"
                
                return StreamingResponse(stream_response_generator(), media_type="text/event-stream")
            else:
                # Non-streaming response
                return ChatCompletionResponse(
                    model=request.model,
                    choices=[ChatCompletionChoice(message=ChatMessage(role="assistant", content=full_content))]
                )

    except httpx.HTTPStatusError as e:
        error_text = e.response.text
        try:
            error_json = e.response.json()
            error_detail = error_json.get("message") or error_json.get("error", {}).get("message") or error_text
        except json.JSONDecodeError:
            error_detail = error_text
        
        print(f"Error from Anymodel API ({e.response.status_code}): {error_detail}")
        status_code = e.response.status_code
        
        if request.stream:
            async def error_stream_gen_http():
                yield f'data: {json.dumps({"error": {"message": error_detail, "type": "anymodel_api_error", "code": status_code}})}\n\n'
                yield "data: [DONE]\n\n"
            return StreamingResponse(error_stream_gen_http(), media_type="text/event-stream", status_code=status_code)
        else:
            raise HTTPException(status_code=status_code, detail=error_detail)
            
    except json.JSONDecodeError as e:
        error_detail = f"Error decoding JSON response from Anymodel: {str(e)}"
        print(error_detail)
        if request.stream:
            async def error_stream_gen_json():
                yield f'data: {json.dumps({"error": {"message": error_detail, "type": "json_decode_error"}})}\n\n'
                yield "data: [DONE]\n\n"
            return StreamingResponse(error_stream_gen_json(), media_type="text/event-stream", status_code=502)
        else:
            raise HTTPException(status_code=502, detail=error_detail)

    except Exception as e:
        error_detail = f"Internal server error during Anymodel call: {str(e)}"
        import traceback
        print(traceback.format_exc())
        if request.stream:
            async def error_stream_gen_generic():
                yield f'data: {json.dumps({"error": {"message": error_detail, "type": "internal_server_error"}})}\n\n'
                yield "data: [DONE]\n\n"
            return StreamingResponse(error_stream_gen_generic(), media_type="text/event-stream", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=error_detail)

if __name__ == "__main__":
    import uvicorn
    
    # Check for required files and create dummy ones if missing
    if not os.path.exists("models.json"):
        print("CRITICAL: models.json not found. This service requires models.json with the specified Anymodel structure.")
    
    if not os.path.exists("anymodel.txt"):
        print("Warning: anymodel.txt not found. Creating a dummy file.")
        with open("anymodel.txt", "w", encoding="utf-8") as f:
            f.write("# Add your Anymodel tokens here, one per line, e.g.: YOUR_TOKEN----email----password\n")
            f.write("<EMAIL>----password # Replace this line\n")
        print("Created dummy anymodel.txt. Please replace its content with valid Anymodel tokens.")

    if not os.path.exists("client_api_keys.json"):
         print("Warning: client_api_keys.json not found. Creating a dummy file.")
         dummy_client_key = f"sk-dummy-{uuid.uuid4().hex}"
         with open("client_api_keys.json", "w", encoding="utf-8") as f:
             json.dump([dummy_client_key], f, indent=2)
         print(f"Created dummy client_api_keys.json with key: {dummy_client_key}. Clients should use this key.")
    
    # Load configurations for startup info
    load_models()
    load_client_api_keys()
    load_anymodel_tokens()

    print("\n--- Anymodel OpenAI API Adapter ---")
    print("Endpoints:")
    print("  GET  /v1/models (Client API Key Auth)")
    print("  GET  /models (No Auth - for compatibility)")
    print("  POST /v1/chat/completions (Client API Key Auth)")
    
    client_keys_preview = list(VALID_CLIENT_KEYS)
    print(f"\nClient API Key(s) for this proxy: {client_keys_preview if client_keys_preview else 'No client API keys loaded. Check client_api_keys.json.'}")
    
    if ANYMODEL_TOKENS:
        print(f"Anymodel Tokens loaded: {len(ANYMODEL_TOKENS)}. First token preview: {ANYMODEL_TOKENS[0][:15]}...")
    else:
        print("Anymodel Tokens: None loaded. Check anymodel.txt.")
        
    loaded_models_count = len(models_data.get("data", []))
    print(f"Loaded base-tier text models: {loaded_models_count}")
    if loaded_models_count == 0:
        print("Ensure 'models.json' is present, correctly formatted, and contains 'base' tier text models.")
    
    print("------------------------------------")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)