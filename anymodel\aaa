import { Application, Router } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";

// Configuration
const ANYMODEL_API_URL = "https://app.anymodel.xyz/api/text";

// 内置模型信息
const MODELS_DATA = {
  object: "list",
  data: [
    {
      id: "anthropic/claude-3-5-sonnet-20240620",
      object: "model",
      created: Math.floor(Date.now() / 1000),
      owned_by: "anthropic"
    }
  ]
};

// Interfaces
interface ChatMessage {
  role: string;
  content: string;
}

interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

interface ChatCompletionChoice {
  message: ChatMessage;
  index: number;
  finish_reason: string;
}

interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatCompletionChoice[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface StreamChoice {
  delta: Record<string, any>;
  index: number;
  finish_reason?: string;
}

interface StreamResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: StreamChoice[];
}

// Utility functions
function generateId(): string {
  return `chatcmpl-${crypto.randomUUID().replace(/-/g, '')}`;
}

function getCurrentTimestamp(): number {
  return Math.floor(Date.now() / 1000);
}

function extractAnymodelToken(authHeader: string | null): string {
  if (!authHeader) {
    throw new Response("Authorization header required", { 
      status: 401,
      headers: { "WWW-Authenticate": "Bearer" }
    });
  }
  
  const token = authHeader.replace("Bearer ", "").trim();
  if (!token) {
    throw new Response("Valid token required in Authorization header", { status: 401 });
  }
  
  return token;
}

function isValidModel(modelId: string): boolean {
  return MODELS_DATA.data.some(model => model.id === modelId);
}

async function streamResponse(chunks: string[]): Promise<ReadableStream<Uint8Array>> {
  const encoder = new TextEncoder();
  let index = 0;
  
  return new ReadableStream({
    start(controller) {
      function pump() {
        if (index < chunks.length) {
          controller.enqueue(encoder.encode(chunks[index]));
          index++;
          setTimeout(pump, 10);
        } else {
          controller.close();
        }
      }
      pump();
    }
  });
}

const router = new Router();

// Routes
router.get("/v1/models", async (ctx) => {
  ctx.response.body = MODELS_DATA;
});

router.get("/models", async (ctx) => {
  ctx.response.body = MODELS_DATA;
});

router.post("/v1/chat/completions", async (ctx) => {
  try {
    // 直接从Authorization header获取anymodel token
    const anymodelToken = extractAnymodelToken(ctx.request.headers.get("authorization"));
    
    const request: ChatCompletionRequest = await ctx.request.body({ type: "json" }).value;
    
    // 验证模型
    if (!isValidModel(request.model)) {
      ctx.response.status = 404;
      ctx.response.body = { 
        error: { 
          message: `Model '${request.model}' not found. Available model: anthropic/claude-3-5-sonnet-20240620`,
          type: "invalid_request_error",
          code: "model_not_found"
        }
      };
      return;
    }
    
    if (!request.messages || request.messages.length === 0) {
      ctx.response.status = 400;
      ctx.response.body = { 
        error: { 
          message: "No messages provided in the request.",
          type: "invalid_request_error"
        }
      };
      return;
    }
    
    // Extract current prompt from last message
    const currentPromptContent = request.messages[request.messages.length - 1].content;
    
    // Build history from previous messages (user/assistant pairs)
    const historyListForAnymodel: any[] = [];
    const processedMessagesForHistory = request.messages.slice(0, -1);
    let i = 0;
    
    while (i < processedMessagesForHistory.length - 1) {
      const msg1 = processedMessagesForHistory[i];
      const msg2 = processedMessagesForHistory[i + 1];
      
      if (msg1.role === "user" && msg2.role === "assistant") {
        historyListForAnymodel.push({
          prompt: msg1.content,
          response: msg2.content,
          functionCalls: [],
          functionResponses: []
        });
        i += 2;
      } else {
        i += 1;
      }
    }
    
    // Construct Anymodel payload
    const anymodelPayload = {
      prompt: currentPromptContent,
      image: null,
      pdf: null,
      options: {
        models: [request.model],
        generatePromptSummary: false,
      },
      history: historyListForAnymodel.length > 0 ? { [request.model]: historyListForAnymodel } : {},
    };
    
    // Set headers for Anymodel API
    const headers = {
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
      "Accept": "application/json",
      "Content-Type": "application/json",
      "authorization": anymodelToken,  // 直接使用用户传入的token
      "origin": "https://app.anymodel.xyz",
      "referer": "https://app.anymodel.xyz/",
    };
    
    try {
      const response = await fetch(ANYMODEL_API_URL, {
        method: "POST",
        headers,
        body: JSON.stringify(anymodelPayload),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = errorText;
        
        // 尝试解析错误信息
        try {
          const errorJson = JSON.parse(errorText);
          errorMessage = errorJson.message || errorJson.error?.message || errorText;
        } catch {
          // 使用原始错误文本
        }
        
        throw new Error(`Anymodel API Error (${response.status}): ${errorMessage}`);
      }
      
      const responseJson = await response.json();
      
      // Extract content from Anymodel response
      let fullContent = "";
      if (responseJson.responses && responseJson.responses.length > 0) {
        const firstResponseItem = responseJson.responses[0];
        if (typeof firstResponseItem === "object" && firstResponseItem !== null) {
          fullContent = firstResponseItem.content || "";
        } else {
          fullContent = String(firstResponseItem || "");
        }
      }
      
      // Handle streaming vs non-streaming response
      if (request.stream) {
        const streamId = generateId();
        const createdTime = getCurrentTimestamp();
        
        const chunks = [
          `data: ${JSON.stringify({
            id: streamId,
            object: "chat.completion.chunk",
            created: createdTime,
            model: request.model,
            choices: [{
              delta: { role: "assistant" },
              index: 0,
              finish_reason: null
            }]
          } as StreamResponse)}\n\n`,
        ];
        
        if (fullContent) {
          chunks.push(`data: ${JSON.stringify({
            id: streamId,
            object: "chat.completion.chunk",
            created: createdTime,
            model: request.model,
            choices: [{
              delta: { content: fullContent },
              index: 0,
              finish_reason: null
            }]
          } as StreamResponse)}\n\n`);
        }
        
        chunks.push(`data: ${JSON.stringify({
          id: streamId,
          object: "chat.completion.chunk",
          created: createdTime,
          model: request.model,
          choices: [{
            delta: {},
            index: 0,
            finish_reason: "stop"
          }]
        } as StreamResponse)}\n\n`);
        
        chunks.push("data: [DONE]\n\n");
        
        ctx.response.headers.set("Content-Type", "text/event-stream");
        ctx.response.headers.set("Cache-Control", "no-cache");
        ctx.response.headers.set("Connection", "keep-alive");
        ctx.response.body = await streamResponse(chunks);
      } else {
        // Non-streaming response
        const chatResponse: ChatCompletionResponse = {
          id: generateId(),
          object: "chat.completion",
          created: getCurrentTimestamp(),
          model: request.model,
          choices: [{
            message: {
              role: "assistant",
              content: fullContent
            },
            index: 0,
            finish_reason: "stop"
          }],
          usage: {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
          }
        };
        
        ctx.response.body = chatResponse;
      }
      
    } catch (error) {
      console.error("Error calling Anymodel API:", error);
      
      const errorDetail = error instanceof Error ? error.message : "Unknown error occurred";
      
      if (request.stream) {
        const errorChunks = [
          `data: ${JSON.stringify({
            error: {
              message: errorDetail,
              type: "anymodel_api_error"
            }
          })}\n\n`,
          "data: [DONE]\n\n"
        ];
        
        ctx.response.headers.set("Content-Type", "text/event-stream");
        ctx.response.status = 502;
        ctx.response.body = await streamResponse(errorChunks);
      } else {
        ctx.response.status = 502;
        ctx.response.body = { error: { message: errorDetail, type: "api_error" } };
      }
    }
    
  } catch (error) {
    if (error instanceof Response) {
      ctx.response.status = error.status;
      ctx.response.body = { error: { message: await error.text() } };
    } else {
      console.error("Unexpected error:", error);
      ctx.response.status = 500;
      ctx.response.body = { error: { message: "Internal server error" } };
    }
  }
});

// Health check endpoint
router.get("/health", (ctx) => {
  ctx.response.body = { 
    status: "ok", 
    model: "anthropic/claude-3-5-sonnet-20240620",
    service: "anymodel-openai-adapter"
  };
});

// Create and configure app
const app = new Application();

app.use(oakCors({
  origin: "*",
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
}));

app.use(router.routes());
app.use(router.allowedMethods());

// Start server
console.log("\n=== Anymodel OpenAI API 转换器 ===");
console.log("📦 模型: anthropic/claude-3-5-sonnet-20240620");
console.log("🔗 直接使用你的 anymodel token");
console.log("\n📡 API端点:");
console.log("  GET  /models - 查看可用模型");
console.log("  POST /v1/chat/completions - 聊天接口");
console.log("  GET  /health - 健康检查");
console.log("\n🚀 服务启动在: http://localhost:8000");
console.log("=====================================");

await app.listen({ port: 8000 });